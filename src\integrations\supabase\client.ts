// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://qwwqcjkvjzgdrdibvjaa.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF3d3Fjamt2anpnZHJkaWJ2amFhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkzMDE3ODIsImV4cCI6MjA2NDg3Nzc4Mn0.L4m9_jEUqb1EVeZEdSETX6TrvTRWJX6FKBDJFvlw6QI";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);