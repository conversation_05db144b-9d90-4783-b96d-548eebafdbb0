<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Preview Generator</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
        }
        
        .social-preview {
            width: 1200px;
            height: 630px;
            background: linear-gradient(135deg, #059669 0%, #047857 50%, #065f46 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .social-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 0%, transparent 50%);
        }
        
        .content {
            text-align: center;
            color: white;
            z-index: 2;
            position: relative;
        }
        
        .logo {
            max-width: 400px;
            max-height: 200px;
            margin-bottom: 30px;
            filter: drop-shadow(0 10px 20px rgba(0,0,0,0.3));
        }
        
        .title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #ffffff, #f0fdf4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 24px;
            margin-bottom: 30px;
            opacity: 0.95;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .features {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-top: 20px;
        }
        
        .feature {
            background: rgba(255,255,255,0.15);
            padding: 15px 25px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            font-size: 16px;
            font-weight: 600;
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }
        
        .crypto-icon {
            position: absolute;
            font-size: 60px;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        .crypto-icon:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .crypto-icon:nth-child(2) { top: 20%; right: 15%; animation-delay: 2s; }
        .crypto-icon:nth-child(3) { bottom: 15%; left: 15%; animation-delay: 4s; }
        .crypto-icon:nth-child(4) { bottom: 25%; right: 10%; animation-delay: 1s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .instructions {
            display: none;
        }
    </style>
</head>
<body>
    <div class="social-preview">
        <div class="decorative-elements">
            <div class="crypto-icon">₿</div>
            <div class="crypto-icon">Ξ</div>
            <div class="crypto-icon">⬡</div>
            <div class="crypto-icon">◊</div>
        </div>
        
        <div class="content">
            <img src="academia web display.png" alt="Web3 Academy" class="logo">
            <div class="title">Web3 Academy</div>
            <div class="subtitle">Master Blockchain & Cryptocurrency</div>
            <div class="features">
                <div class="feature">🚀 DeFi Mastery</div>
                <div class="feature">💎 NFT Creation</div>
                <div class="feature">⚡ Smart Contracts</div>
            </div>
        </div>
        
        <div class="instructions">
            Instructions: Take a screenshot of this preview at 1200x630px and save it as "academia-social-preview.png" in the public folder.
        </div>
    </div>
</body>
</html>
