@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced PWA Safe Area Support for iPhone and Android */
@supports (padding: max(0px)) {
  /* Basic safe area padding */
  .pb-safe {
    padding-bottom: max(1.5rem, calc(1rem + env(safe-area-inset-bottom)));
  }

  .pt-safe {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  .pl-safe {
    padding-left: max(1rem, env(safe-area-inset-left));
  }

  .pr-safe {
    padding-right: max(1rem, env(safe-area-inset-right));
  }

  /* Mobile header safe area - fixed positioning with top safe area */
  .mobile-header-safe {
    padding-top: max(0.75rem, env(safe-area-inset-top));
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }

  /* Content area that accounts for fixed header */
  .content-safe-top {
    padding-top: max(5rem, calc(4rem + env(safe-area-inset-top)));
  }

  /* Bottom navigation - extends to bottom with home indicator inside navbar */
  .bottom-nav-safe {
    padding-bottom: max(1.5rem, calc(1rem + env(safe-area-inset-bottom)));
    padding-left: max(0rem, env(safe-area-inset-left));
    padding-right: max(0rem, env(safe-area-inset-right));
    bottom: 0;
    margin-bottom: 0;
  }

  /* Content safe area for pages with bottom navigation */
  .content-safe-bottom {
    padding-bottom: max(6rem, calc(4rem + env(safe-area-inset-bottom) + 1rem));
    margin-bottom: 0;
  }

  /* Combined safe area for content with both header and bottom nav */
  .content-safe-full {
    padding-top: max(5rem, calc(4rem + env(safe-area-inset-top)));
    padding-bottom: max(6rem, calc(4rem + env(safe-area-inset-bottom) + 1rem));
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }

  /* PWA standalone mode specific styles */
  @media (display-mode: standalone) {
    .pwa-content-safe {
      padding-top: max(5.5rem, calc(4.5rem + env(safe-area-inset-top)));
      padding-bottom: max(6.5rem, calc(5rem + env(safe-area-inset-bottom)));
    }

    .pwa-header-safe {
      padding-top: max(1rem, calc(0.5rem + env(safe-area-inset-top)));
    }
  }
}

/* Fallback for browsers that don't support env() */
@supports not (padding: env(safe-area-inset-bottom)) {
  .pb-safe {
    padding-bottom: 1.5rem;
  }

  .pt-safe {
    padding-top: 1rem;
  }

  .mobile-header-safe {
    padding-top: 0.75rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .content-safe-top {
    padding-top: 5rem;
  }

  .bottom-nav-safe {
    padding-bottom: 1.5rem;
    margin-bottom: 0;
  }

  .content-safe-bottom {
    padding-bottom: 6rem;
    margin-bottom: 0;
  }

  .content-safe-full {
    padding-top: 5rem;
    padding-bottom: 6rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .pwa-content-safe {
    padding-top: 5.5rem;
    padding-bottom: 6.5rem;
  }

  .pwa-header-safe {
    padding-top: 1rem;
  }
}

/* PWA-specific styles for better mobile experience */
@media (display-mode: standalone) {
  /* Hide scrollbars in PWA mode for cleaner look */
  ::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  /* Ensure full viewport usage */
  html, body {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
    overflow-x: hidden;
  }

  /* Prevent overscroll bounce on iOS */
  body {
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }
}

/* Utility classes for PWA layout */
.min-h-screen-safe {
  min-height: 100vh;
  min-height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
}

.h-screen-safe {
  height: 100vh;
  height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
}

/* Scrollable content area that respects safe areas */
.scrollable-content {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}